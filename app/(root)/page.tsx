"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Database,
  MapPin,
  FileText,
  Settings,
  Users,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Mail,
  ExternalLink,
  Filter,
  Upload,
  Zap,
  BarChart3,
} from "lucide-react";
import Link from "next/link";

export default function DashboardPage() {
  return (
    <div className="mx-auto p-4 max-w-6xl space-y-8">
      {/* Header Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">
          Welcome to the Bianchi Lead Management Tool
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          A comprehensive lead generation and management platform built by{" "}
          <span className="font-semibold text-primary">ETH Juniors</span> for
          Bianchi. Discover, track, and manage restaurant leads across
          Switzerland with our integrated tools.
        </p>
      </div>

      {/* Introduction Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-6 w-6 text-primary" />
            About This Tool
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-700">
            This lead management system was developed by ETH Juniors, the
            student consultancy of ETH Zurich, specifically for Bianchi to
            streamline their restaurant lead generation and management
            processes. The platform combines automated lead discovery with
            powerful management tools to help you identify and track potential
            restaurant clients across Switzerland.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Search className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-blue-900">
                Automated Discovery
              </h3>
              <p className="text-sm text-blue-700">
                Find new restaurant leads using Google Places API
              </p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <Database className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-green-900">
                Centralized Management
              </h3>
              <p className="text-sm text-green-700">
                Organize and track all leads in one place
              </p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <BarChart3 className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold text-purple-900">
                Data Integration
              </h3>
              <p className="text-sm text-purple-700">
                Sync with ERP systems and external data sources
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-6 w-6 text-primary" />
            Key Features
          </CardTitle>
          <CardDescription>
            Explore the powerful tools available in this lead management
            platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Lead Generation */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Search className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Lead Generation</h3>
                  <Badge variant="secondary" className="text-xs">
                    Two Methods Available
                  </Badge>
                </div>
              </div>
              <div className="space-y-3 ml-11">
                <div>
                  <h4 className="font-medium text-blue-900">Location Search</h4>
                  <p className="text-sm text-gray-600">
                    Search for restaurants in specific cities or regions using
                    Google Places API. Perfect for targeted lead discovery in
                    known markets.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-blue-900">
                    Precomputed Search
                  </h4>
                  <p className="text-sm text-gray-600">
                    Use pre-calculated search points across Switzerland for
                    comprehensive coverage. Includes polygon selection and
                    advanced filtering options.
                  </p>
                </div>
              </div>
            </div>

            {/* Leads Management */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Database className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Leads Management</h3>
                  <Badge variant="secondary" className="text-xs">
                    Full CRUD Operations
                  </Badge>
                </div>
              </div>
              <div className="space-y-3 ml-11">
                <div>
                  <h4 className="font-medium text-green-900">
                    Table & Map Views
                  </h4>
                  <p className="text-sm text-gray-600">
                    View leads in a comprehensive table with sorting, filtering,
                    and pagination, or visualize them on an interactive map.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-green-900">
                    Advanced Filtering
                  </h4>
                  <p className="text-sm text-gray-600">
                    Filter by canton, business type, source, date ranges, and
                    more. Includes duplicate detection and color-coded address
                    groups.
                  </p>
                </div>
              </div>
            </div>

            {/* ERP Sync */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Upload className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">ERP Sync</h3>
                  <Badge variant="secondary" className="text-xs">
                    CSV Import
                  </Badge>
                </div>
              </div>
              <div className="space-y-3 ml-11">
                <div>
                  <h4 className="font-medium text-purple-900">CSV Import</h4>
                  <p className="text-sm text-gray-600">
                    Import leads from your ERP system via CSV files. Automatic
                    field mapping and duplicate detection included.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-purple-900">
                    Auto Place ID Fetching
                  </h4>
                  <p className="text-sm text-gray-600">
                    Automatically fetch Google Place IDs for imported leads to
                    enhance data completeness and enable map visualization.
                  </p>
                </div>
              </div>
            </div>

            {/* Google Places ID Management */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <MapPin className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">
                    Google Places ID Management
                  </h3>
                  <Badge variant="secondary" className="text-xs">
                    Bulk Operations
                  </Badge>
                </div>
              </div>
              <div className="space-y-3 ml-11">
                <div>
                  <h4 className="font-medium text-orange-900">
                    Bulk Assignment
                  </h4>
                  <p className="text-sm text-gray-600">
                    Automatically find and assign Google Place IDs to leads that
                    lack them. Cost-optimized with confidence scoring.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-orange-900">
                    Quality Control
                  </h4>
                  <p className="text-sm text-gray-600">
                    Review and confirm Place ID assignments before saving.
                    Includes direct Google Maps verification links.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* How-to-Use Guide */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-6 w-6 text-primary" />
            How to Use This Tool
          </CardTitle>
          <CardDescription>
            Step-by-step guide to effectively navigate and utilize each feature
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Getting Started */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Getting Started
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">1. View Existing Leads</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Start by exploring your current lead database to understand
                  what data you already have.
                </p>
                <Link href="/leads">
                  <Button variant="outline" size="sm" className="w-full">
                    <Database className="h-4 w-4 mr-2" />
                    Go to Leads Page
                  </Button>
                </Link>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">2. Generate New Leads</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Use the lead generation tools to discover new restaurant
                  prospects in your target areas.
                </p>
                <Link href="/lead-generator">
                  <Button variant="outline" size="sm" className="w-full">
                    <Search className="h-4 w-4 mr-2" />
                    Start Lead Generation
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Lead Generation Workflow */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <Search className="h-5 w-5 text-blue-600" />
              Lead Generation Workflow
            </h3>
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                  1
                </div>
                <div>
                  <h4 className="font-medium">Choose Your Method</h4>
                  <p className="text-sm text-gray-600">
                    Select between "Location Search" for targeted city-based
                    searches or "Precomputed" for comprehensive coverage using
                    pre-calculated points.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                  2
                </div>
                <div>
                  <h4 className="font-medium">Configure Search Parameters</h4>
                  <p className="text-sm text-gray-600">
                    Set your search criteria including business types
                    (restaurant, cafe, etc.), location radius, and maximum
                    results per search point.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                  3
                </div>
                <div>
                  <h4 className="font-medium">Review and Save Results</h4>
                  <p className="text-sm text-gray-600">
                    Review the discovered leads, select the ones you want to add
                    to your database, and save them for future follow-up.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* ERP Sync Process */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <Upload className="h-5 w-5 text-purple-600" />
              ERP Sync Process
            </h3>
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-semibold text-sm">
                  1
                </div>
                <div>
                  <h4 className="font-medium">Prepare Your CSV File</h4>
                  <p className="text-sm text-gray-600">
                    Export your leads from your ERP system in CSV format. The
                    system supports German field names and automatic field
                    mapping.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-semibold text-sm">
                  2
                </div>
                <div>
                  <h4 className="font-medium">Upload and Review</h4>
                  <p className="text-sm text-gray-600">
                    Upload your CSV file, review the parsed data, and check for
                    any existing duplicates before importing.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-semibold text-sm">
                  3
                </div>
                <div>
                  <h4 className="font-medium">Import and Enhance</h4>
                  <p className="text-sm text-gray-600">
                    Import the leads and automatically fetch Google Place IDs to
                    enhance the data with location information and enable map
                    visualization.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Data Management Tips */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <Filter className="h-5 w-5 text-green-600" />
              Data Management Tips
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">
                  Filtering & Search
                </h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• Use canton filters to focus on specific regions</li>
                  <li>
                    • Filter by address group (Kunde, Werbekunde) for targeted
                    outreach
                  </li>
                  <li>• Search by business name or address for quick lookup</li>
                  <li>• Use date filters to find recently added leads</li>
                </ul>
              </div>
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Data Quality</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Regularly run bulk Google Place ID assignment</li>
                  <li>• Review and resolve duplicate entries</li>
                  <li>• Export data for backup and external analysis</li>
                  <li>• Monitor system logs for import/export activities</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowRight className="h-6 w-6 text-primary" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Jump directly to the most commonly used features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link href="/leads" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3 mb-2">
                  <Database className="h-6 w-6 text-blue-600" />
                  <h3 className="font-semibold">View Leads</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Browse and manage your lead database
                </p>
              </div>
            </Link>

            <Link href="/lead-generator" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3 mb-2">
                  <Search className="h-6 w-6 text-green-600" />
                  <h3 className="font-semibold">Generate Leads</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Discover new restaurant prospects
                </p>
              </div>
            </Link>

            <Link href="/leads/erp-sync" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3 mb-2">
                  <Upload className="h-6 w-6 text-purple-600" />
                  <h3 className="font-semibold">ERP Sync</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Import leads from your ERP system
                </p>
              </div>
            </Link>

            <Link href="/leads/bulk-place-id" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3 mb-2">
                  <MapPin className="h-6 w-6 text-orange-600" />
                  <h3 className="font-semibold">Bulk Place IDs</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Assign Google Place IDs to leads
                </p>
              </div>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Additional Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-6 w-6 text-primary" />
              System Logs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Monitor all system activities including lead imports, exports, and
              API operations. Track user actions and system performance.
            </p>
            <Link href="/logs">
              <Button variant="outline" className="w-full">
                <AlertCircle className="h-4 w-4 mr-2" />
                View System Logs
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-6 w-6 text-primary" />
              Settings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Configure system preferences, API settings, and user preferences.
              Currently in development for future releases.
            </p>
            <Link href="/settings">
              <Button variant="outline" className="w-full" disabled>
                <Settings className="h-4 w-4 mr-2" />
                Settings (Coming Soon)
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Contact Information */}
      <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-6 w-6 text-primary" />
            Support & Bug Reports
          </CardTitle>
          <CardDescription>
            Need help or found an issue? We're here to assist you.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-white rounded-lg border">
              <h3 className="font-semibold mb-2 flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                Report Bugs or Issues
              </h3>
              <p className="text-gray-600 mb-3">
                If you encounter any problems, bugs, or have suggestions for
                improvements, please don't hesitate to contact our development
                team.
              </p>
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md">
                <Mail className="h-4 w-4 text-gray-600" />
                <span className="font-medium"><EMAIL></span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    window.open(
                      "mailto:<EMAIL>?subject=Bianchi Lead Tool - Bug Report"
                    )
                  }
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Send Email
                </Button>
              </div>
            </div>

            <div className="p-4 bg-white rounded-lg border">
              <h3 className="font-semibold mb-2 flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                About ETH Juniors
              </h3>
              <p className="text-gray-600">
                This tool was developed by ETH Juniors, the student consultancy
                of ETH Zurich. We specialize in creating innovative solutions
                for businesses while providing valuable real-world experience
                for our student consultants.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
