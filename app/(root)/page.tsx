"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Search,
  Database,
  MapPin,
  FileText,
  Settings,
  Users,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Mail,
  ExternalLink,
  Filter,
  Upload,
  Zap,
  BarChart3,
  ChevronDown,
  ChevronRight,
  BookOpen,
  Target,
  Workflow,
  HelpCircle,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function LandingPage() {
  const [leadGenExpanded, setLeadGenExpanded] = useState(false);
  const [leadsManagementExpanded, setLeadsManagementExpanded] = useState(false);
  const [workflowExpanded, setWorkflowExpanded] = useState(false);
  const [advancedFeaturesExpanded, setAdvancedFeaturesExpanded] =
    useState(false);

  return (
    <div className="mx-auto p-4 max-w-6xl space-y-8">
      {/* Header Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">
          Bianchi Lead Management Tool
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          A comprehensive lead generation and management platform for
          discovering, tracking, and managing restaurant leads across
          Switzerland.
        </p>
        <div className="flex justify-center gap-4 mt-6">
          <Link href="/lead-generator">
            <Button size="lg" className="gap-2">
              <Search className="h-5 w-5" />
              Start Generating Leads
            </Button>
          </Link>
          <Link href="/leads">
            <Button variant="outline" size="lg" className="gap-2">
              <Database className="h-5 w-5" />
              View Existing Leads
            </Button>
          </Link>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="text-center p-6 bg-blue-50 rounded-lg border border-blue-200">
          <Search className="h-10 w-10 text-blue-600 mx-auto mb-3" />
          <h3 className="font-semibold text-blue-900 mb-2">Lead Generation</h3>
          <p className="text-sm text-blue-700">
            Discover new restaurant prospects using advanced search tools and
            Google Places API
          </p>
        </div>
        <div className="text-center p-6 bg-green-50 rounded-lg border border-green-200">
          <Database className="h-10 w-10 text-green-600 mx-auto mb-3" />
          <h3 className="font-semibold text-green-900 mb-2">Lead Management</h3>
          <p className="text-sm text-green-700">
            Organize, filter, and track all leads with powerful management tools
          </p>
        </div>
        <div className="text-center p-6 bg-purple-50 rounded-lg border border-purple-200">
          <BarChart3 className="h-10 w-10 text-purple-600 mx-auto mb-3" />
          <h3 className="font-semibold text-purple-900 mb-2">
            Data Integration
          </h3>
          <p className="text-sm text-purple-700">
            Sync with ERP systems and enhance data with Google Places
            integration
          </p>
        </div>
      </div>

      {/* Lead Generation Section */}
      <Card>
        <CardHeader>
          <Collapsible open={leadGenExpanded} onOpenChange={setLeadGenExpanded}>
            <CollapsibleTrigger asChild>
              <div className="flex items-center justify-between w-full cursor-pointer hover:bg-gray-50 p-2 rounded-md -m-2">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Search className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-left">
                      Lead Generation Tools
                    </CardTitle>
                    <CardDescription className="text-left">
                      Discover new restaurant prospects with powerful search
                      capabilities
                    </CardDescription>
                  </div>
                </div>
                {leadGenExpanded ? (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-gray-500" />
                )}
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4">
              <CardContent className="pt-0">
                <div className="space-y-6">
                  {/* Method Overview */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="p-4 border rounded-lg bg-blue-50">
                      <div className="flex items-center gap-3 mb-3">
                        <Target className="h-5 w-5 text-blue-600" />
                        <h3 className="font-semibold text-blue-900">
                          Location Search
                        </h3>
                        <Badge variant="secondary" className="text-xs">
                          Targeted
                        </Badge>
                      </div>
                      <p className="text-sm text-blue-800 mb-3">
                        Search for restaurants in specific cities or regions
                        using Google Places API. Perfect for targeted lead
                        discovery in known markets.
                      </p>
                      <ul className="text-xs text-blue-700 space-y-1">
                        <li>
                          • City-based searches with preselected locations
                        </li>
                        <li>• Maximum 20 results per search (latest API)</li>
                        <li>• Maximum 60 results per search (legacy API)</li>
                        <li>• Real-time API call progress tracking</li>
                      </ul>
                    </div>

                    <div className="p-4 border rounded-lg bg-green-50">
                      <div className="flex items-center gap-3 mb-3">
                        <MapPin className="h-5 w-5 text-green-600" />
                        <h3 className="font-semibold text-green-900">
                          Precomputed Search
                        </h3>
                        <Badge variant="secondary" className="text-xs">
                          Comprehensive
                        </Badge>
                      </div>
                      <p className="text-sm text-green-800 mb-3">
                        Use pre-calculated search points across Switzerland for
                        comprehensive coverage. Includes advanced filtering and
                        polygon selection.
                      </p>
                      <ul className="text-xs text-green-700 space-y-1">
                        <li>• Polygon selection for custom areas</li>
                        <li>• Paginated results (100 per page)</li>
                        <li>• Real-time statistics and progress tracking</li>
                        <li>• Periodic saving to prevent data loss</li>
                      </ul>
                    </div>
                  </div>

                  {/* Step-by-step Guide */}
                  <div className="border-t pt-6">
                    <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <BookOpen className="h-5 w-5 text-blue-600" />
                      How to Generate Leads
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                          1
                        </div>
                        <div>
                          <h4 className="font-medium">
                            Choose Your Search Method
                          </h4>
                          <p className="text-sm text-gray-600">
                            Select "Location Search" for targeted city-based
                            searches or "Precomputed" for comprehensive coverage
                            using pre-calculated points across Switzerland.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                          2
                        </div>
                        <div>
                          <h4 className="font-medium">
                            Configure Search Parameters
                          </h4>
                          <p className="text-sm text-gray-600">
                            Set business types (restaurant, cafe, etc.),
                            location radius, and maximum results. For
                            Precomputed searches, optionally draw polygons to
                            filter specific areas.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                          3
                        </div>
                        <div>
                          <h4 className="font-medium">
                            Monitor Progress & Review Results
                          </h4>
                          <p className="text-sm text-gray-600">
                            Watch real-time progress updates and review
                            discovered leads. Results are automatically saved to
                            localStorage and can be exported to your database.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Quick Start */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium mb-3">Quick Start</h4>
                    <div className="flex gap-3">
                      <Link href="/lead-generator">
                        <Button className="gap-2">
                          <Search className="h-4 w-4" />
                          Start Lead Generation
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </CardHeader>
      </Card>

      {/* Leads Management Section */}
      <Card>
        <CardHeader>
          <Collapsible
            open={leadsManagementExpanded}
            onOpenChange={setLeadsManagementExpanded}
          >
            <CollapsibleTrigger asChild>
              <div className="flex items-center justify-between w-full cursor-pointer hover:bg-gray-50 p-2 rounded-md -m-2">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Database className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <CardTitle className="text-left">
                      Leads Management & Organization
                    </CardTitle>
                    <CardDescription className="text-left">
                      Comprehensive tools for managing, filtering, and
                      organizing your lead database
                    </CardDescription>
                  </div>
                </div>
                {leadsManagementExpanded ? (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-gray-500" />
                )}
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4">
              <CardContent className="pt-0">
                <div className="space-y-6">
                  {/* Core Features */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="p-4 border rounded-lg bg-green-50">
                      <div className="flex items-center gap-3 mb-3">
                        <Database className="h-5 w-5 text-green-600" />
                        <h3 className="font-semibold text-green-900">
                          Table & Map Views
                        </h3>
                        <Badge variant="secondary" className="text-xs">
                          Interactive
                        </Badge>
                      </div>
                      <p className="text-sm text-green-800 mb-3">
                        View leads in a comprehensive table with sorting,
                        filtering, and pagination, or visualize them on an
                        interactive map with location markers.
                      </p>
                      <ul className="text-xs text-green-700 space-y-1">
                        <li>• Sticky headers with vertical scrolling</li>
                        <li>• Color-coded rows by address group</li>
                        <li>• Expandable duplicate lead sections</li>
                        <li>• Real-time search and filtering</li>
                      </ul>
                    </div>

                    <div className="p-4 border rounded-lg bg-blue-50">
                      <div className="flex items-center gap-3 mb-3">
                        <Filter className="h-5 w-5 text-blue-600" />
                        <h3 className="font-semibold text-blue-900">
                          Advanced Filtering
                        </h3>
                        <Badge variant="secondary" className="text-xs">
                          Dynamic
                        </Badge>
                      </div>
                      <p className="text-sm text-blue-800 mb-3">
                        Filter by canton, business type, source, date ranges,
                        and more. Includes duplicate detection and intelligent
                        grouping.
                      </p>
                      <ul className="text-xs text-blue-700 space-y-1">
                        <li>• Canton and region-based filtering</li>
                        <li>• Address group classification</li>
                        <li>• Date range and source filtering</li>
                        <li>• Google Place ID status filtering</li>
                      </ul>
                    </div>
                  </div>

                  {/* Data Integration */}
                  <div className="border-t pt-6">
                    <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <Upload className="h-5 w-5 text-purple-600" />
                      Data Integration & Enhancement
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-4 border rounded-lg">
                        <h4 className="font-medium text-purple-900 mb-2">
                          ERP Sync
                        </h4>
                        <p className="text-sm text-gray-600 mb-3">
                          Import leads from your ERP system via CSV files with
                          automatic field mapping and duplicate detection.
                        </p>
                        <ul className="text-xs text-gray-600 space-y-1">
                          <li>• German field name support</li>
                          <li>• Automatic duplicate detection</li>
                          <li>• Batch processing with progress tracking</li>
                        </ul>
                      </div>

                      <div className="p-4 border rounded-lg">
                        <h4 className="font-medium text-orange-900 mb-2">
                          Google Places Integration
                        </h4>
                        <p className="text-sm text-gray-600 mb-3">
                          Automatically fetch and assign Google Place IDs to
                          enhance data completeness and enable map
                          visualization.
                        </p>
                        <ul className="text-xs text-gray-600 space-y-1">
                          <li>• Bulk Place ID assignment</li>
                          <li>• Cost-optimized API usage</li>
                          <li>• Quality control and verification</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium mb-3">Quick Actions</h4>
                    <div className="flex gap-3 flex-wrap">
                      <Link href="/leads">
                        <Button className="gap-2">
                          <Database className="h-4 w-4" />
                          View Leads Table
                        </Button>
                      </Link>
                      <Link href="/leads/erp-sync">
                        <Button variant="outline" className="gap-2">
                          <Upload className="h-4 w-4" />
                          ERP Sync
                        </Button>
                      </Link>
                      <Link href="/leads/bulk-place-id">
                        <Button variant="outline" className="gap-2">
                          <MapPin className="h-4 w-4" />
                          Bulk Place IDs
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </CardHeader>
      </Card>

      {/* Progressive User Guidance */}
      <Card>
        <CardHeader>
          <Collapsible
            open={workflowExpanded}
            onOpenChange={setWorkflowExpanded}
          >
            <CollapsibleTrigger asChild>
              <div className="flex items-center justify-between w-full cursor-pointer hover:bg-gray-50 p-2 rounded-md -m-2">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Workflow className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <CardTitle className="text-left">
                      User Workflow & Guidance
                    </CardTitle>
                    <CardDescription className="text-left">
                      Step-by-step guides and best practices for effective lead
                      management
                    </CardDescription>
                  </div>
                </div>
                {workflowExpanded ? (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-gray-500" />
                )}
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4">
              <CardContent className="pt-0">
                <div className="space-y-6">
                  {/* Getting Started */}
                  <div>
                    <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      Getting Started - Your First Steps
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border rounded-lg bg-green-50">
                        <h4 className="font-medium mb-2 text-green-900">
                          1. Explore Your Data
                        </h4>
                        <p className="text-sm text-green-800 mb-3">
                          Start by exploring your current lead database to
                          understand existing data, identify gaps, and plan your
                          lead generation strategy.
                        </p>
                        <Link href="/leads">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full border-green-300 text-green-700 hover:bg-green-100"
                          >
                            <Database className="h-4 w-4 mr-2" />
                            View Leads Database
                          </Button>
                        </Link>
                      </div>
                      <div className="p-4 border rounded-lg bg-blue-50">
                        <h4 className="font-medium mb-2 text-blue-900">
                          2. Generate New Leads
                        </h4>
                        <p className="text-sm text-blue-800 mb-3">
                          Use the lead generation tools to discover new
                          restaurant prospects in your target areas and expand
                          your potential client base.
                        </p>
                        <Link href="/lead-generator">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full border-blue-300 text-blue-700 hover:bg-blue-100"
                          >
                            <Search className="h-4 w-4 mr-2" />
                            Start Lead Generation
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>

                  {/* Complete Workflow */}
                  <div className="border-t pt-6">
                    <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <Workflow className="h-5 w-5 text-orange-600" />
                      Complete Lead Management Workflow
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-semibold text-sm">
                          1
                        </div>
                        <div>
                          <h4 className="font-medium">
                            Data Assessment & Planning
                          </h4>
                          <p className="text-sm text-gray-600">
                            Review existing leads, identify target regions, and
                            plan your lead generation strategy based on current
                            data gaps and business objectives.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-semibold text-sm">
                          2
                        </div>
                        <div>
                          <h4 className="font-medium">
                            Lead Generation & Discovery
                          </h4>
                          <p className="text-sm text-gray-600">
                            Use Location Search for targeted areas or
                            Precomputed Search for comprehensive coverage.
                            Configure search parameters and monitor progress in
                            real-time.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-semibold text-sm">
                          3
                        </div>
                        <div>
                          <h4 className="font-medium">
                            Data Integration & Enhancement
                          </h4>
                          <p className="text-sm text-gray-600">
                            Import ERP data via CSV, assign Google Place IDs for
                            location accuracy, and resolve duplicates to
                            maintain data quality.
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-semibold text-sm">
                          4
                        </div>
                        <div>
                          <h4 className="font-medium">
                            Organization & Analysis
                          </h4>
                          <p className="text-sm text-gray-600">
                            Use advanced filtering, export data for analysis,
                            and monitor system logs to track activities and
                            maintain optimal performance.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Best Practices */}
                  <div className="border-t pt-6">
                    <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <HelpCircle className="h-5 w-5 text-blue-600" />
                      Best Practices & Tips
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <h4 className="font-medium text-blue-900 mb-2">
                          Efficient Lead Generation
                        </h4>
                        <ul className="text-sm text-blue-800 space-y-1">
                          <li>
                            • Start with Location Search for known target areas
                          </li>
                          <li>
                            • Use Precomputed Search for comprehensive coverage
                          </li>
                          <li>• Monitor API usage to optimize costs</li>
                          <li>
                            • Save progress regularly during long searches
                          </li>
                        </ul>
                      </div>
                      <div className="p-4 bg-green-50 rounded-lg">
                        <h4 className="font-medium text-green-900 mb-2">
                          Data Quality Management
                        </h4>
                        <ul className="text-sm text-green-800 space-y-1">
                          <li>
                            • Regularly run bulk Google Place ID assignment
                          </li>
                          <li>• Review and resolve duplicate entries</li>
                          <li>
                            • Use filters to focus on high-value prospects
                          </li>
                          <li>
                            • Export data for backup and external analysis
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </CardHeader>
      </Card>

      {/* Advanced Features & Tools */}
      <Card>
        <CardHeader>
          <Collapsible
            open={advancedFeaturesExpanded}
            onOpenChange={setAdvancedFeaturesExpanded}
          >
            <CollapsibleTrigger asChild>
              <div className="flex items-center justify-between w-full cursor-pointer hover:bg-gray-50 p-2 rounded-md -m-2">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Settings className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <CardTitle className="text-left">
                      Advanced Features & System Tools
                    </CardTitle>
                    <CardDescription className="text-left">
                      Additional tools for system monitoring, troubleshooting,
                      and advanced operations
                    </CardDescription>
                  </div>
                </div>
                {advancedFeaturesExpanded ? (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-gray-500" />
                )}
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4">
              <CardContent className="pt-0">
                <div className="space-y-6">
                  {/* System Tools */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="p-4 border rounded-lg bg-gray-50">
                      <div className="flex items-center gap-3 mb-3">
                        <FileText className="h-5 w-5 text-gray-600" />
                        <h3 className="font-semibold text-gray-900">
                          System Logs
                        </h3>
                        <Badge variant="secondary" className="text-xs">
                          Monitoring
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-700 mb-3">
                        Monitor all system activities including lead imports,
                        exports, API operations, and user actions for
                        performance tracking and troubleshooting.
                      </p>
                      <Link href="/logs">
                        <Button variant="outline" className="w-full">
                          <AlertCircle className="h-4 w-4 mr-2" />
                          View System Logs
                        </Button>
                      </Link>
                    </div>

                    <div className="p-4 border rounded-lg bg-amber-50">
                      <div className="flex items-center gap-3 mb-3">
                        <Settings className="h-5 w-5 text-amber-600" />
                        <h3 className="font-semibold text-amber-900">
                          Settings
                        </h3>
                        <Badge
                          variant="outline"
                          className="text-xs border-amber-300 text-amber-700"
                        >
                          Coming Soon
                        </Badge>
                      </div>
                      <p className="text-sm text-amber-800 mb-3">
                        Configure system preferences, API settings, and user
                        preferences. Advanced configuration options for power
                        users.
                      </p>
                      <Link href="/settings">
                        <Button variant="outline" className="w-full" disabled>
                          <Settings className="h-4 w-4 mr-2" />
                          Settings (In Development)
                        </Button>
                      </Link>
                    </div>
                  </div>

                  {/* Troubleshooting */}
                  <div className="border-t pt-6">
                    <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <HelpCircle className="h-5 w-5 text-blue-600" />
                      Troubleshooting & Common Issues
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <h4 className="font-medium text-blue-900 mb-2">
                          Performance Issues
                        </h4>
                        <ul className="text-sm text-blue-800 space-y-1">
                          <li>• Check system logs for error patterns</li>
                          <li>• Reduce batch sizes for large operations</li>
                          <li>• Clear browser cache and localStorage</li>
                          <li>• Monitor API rate limits and usage</li>
                        </ul>
                      </div>
                      <div className="p-4 bg-orange-50 rounded-lg">
                        <h4 className="font-medium text-orange-900 mb-2">
                          Data Quality Issues
                        </h4>
                        <ul className="text-sm text-orange-800 space-y-1">
                          <li>• Run duplicate detection regularly</li>
                          <li>• Verify Google Place ID assignments</li>
                          <li>• Check CSV import field mappings</li>
                          <li>• Review filter configurations</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </CardHeader>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowRight className="h-6 w-6 text-primary" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Jump directly to the most commonly used features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link href="/leads" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3 mb-2">
                  <Database className="h-6 w-6 text-blue-600" />
                  <h3 className="font-semibold">View Leads</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Browse and manage your lead database
                </p>
              </div>
            </Link>

            <Link href="/lead-generator" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3 mb-2">
                  <Search className="h-6 w-6 text-green-600" />
                  <h3 className="font-semibold">Generate Leads</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Discover new restaurant prospects
                </p>
              </div>
            </Link>

            <Link href="/leads/erp-sync" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3 mb-2">
                  <Upload className="h-6 w-6 text-purple-600" />
                  <h3 className="font-semibold">ERP Sync</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Import leads from your ERP system
                </p>
              </div>
            </Link>

            <Link href="/leads/bulk-place-id" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3 mb-2">
                  <MapPin className="h-6 w-6 text-orange-600" />
                  <h3 className="font-semibold">Bulk Place IDs</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Assign Google Place IDs to leads
                </p>
              </div>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-6 w-6 text-primary" />
            Support & Bug Reports
          </CardTitle>
          <CardDescription>
            Need help or found an issue? We're here to assist you.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-white rounded-lg border">
              <h3 className="font-semibold mb-2 flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                Report Bugs or Issues
              </h3>
              <p className="text-gray-600 mb-3">
                If you encounter any problems, bugs, or have suggestions for
                improvements, please don't hesitate to contact our development
                team.
              </p>
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md">
                <Mail className="h-4 w-4 text-gray-600" />
                <span className="font-medium"><EMAIL></span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    window.open(
                      "mailto:<EMAIL>?subject=Bianchi Lead Tool - Bug Report"
                    )
                  }
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Send Email
                </Button>
              </div>
            </div>

            <div className="p-4 bg-white rounded-lg border">
              <h3 className="font-semibold mb-2 flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                About ETH Juniors
              </h3>
              <p className="text-gray-600">
                This tool was developed by ETH Juniors, the student consultancy
                of ETH Zurich. We specialize in creating innovative solutions
                for businesses while providing valuable real-world experience
                for our student consultants.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
