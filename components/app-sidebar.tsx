"use client";

import * as React from "react";
import Image from "next/image";
import {
  Home,
  List,
  AlertCircle,
  Settings,
  Search,
  Eye,
  Lock,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";

const menuItems = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home,
    status: "preview" as const,
    statusText: "Preview Mode",
  },
  {
    title: "Leads",
    href: "/leads",
    icon: List,
    status: "active" as const,
  },
  {
    title: "Lead Generator",
    href: "/lead-generator",
    icon: Search,
    status: "active" as const,
  },
  {
    title: "Logs",
    href: "/logs",
    icon: AlertCircle,
    status: "active" as const,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
    status: "not-implemented" as const,
    statusText: "Not Implemented",
  },
];

export function AppSidebar() {
  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <Link
          href="/"
          className="flex items-center gap-2 p-2 hover:opacity-80 transition-opacity"
        >
          <div className="flex h-8 w-8 shrink-0 items-center justify-center">
            <Image
              src="/images/Bianchi_Logo.webp"
              alt="Bianchi Logo"
              width={32}
              height={32}
              priority
              className="object-contain"
            />
          </div>
          <span className="font-semibold text-lg whitespace-nowrap group-data-[collapsible=icon]:hidden">
            Bianchi Leads
          </span>
        </Link>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="group-data-[collapsible=icon]:hidden">
            Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    tooltip={item.statusText || item.title}
                  >
                    <Link href={item.href} className="flex items-center gap-2">
                      <item.icon className="h-4 w-4 shrink-0" />
                      <span className="group-data-[collapsible=icon]:hidden flex-1">
                        {item.title}
                      </span>
                      {item.status !== "active" && (
                        <div className="group-data-[collapsible=icon]:hidden">
                          {item.status === "preview" && (
                            <Badge
                              variant="outline"
                              className="text-xs border-blue-300 text-blue-700 bg-blue-50"
                            >
                              <Eye className="h-2 w-2 mr-1" />
                              TBI
                            </Badge>
                          )}
                          {item.status === "not-implemented" && (
                            <Badge
                              variant="outline"
                              className="text-xs border-amber-300 text-amber-700 bg-amber-50"
                            >
                              <Lock className="h-2 w-2 mr-1" />
                              TBI
                            </Badge>
                          )}
                        </div>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="border-t p-4">
        <div className="text-xs text-muted-foreground group-data-[collapsible=icon]:hidden">
          Bianchi Leads Tool v1.0
        </div>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
